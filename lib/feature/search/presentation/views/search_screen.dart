import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/widgets/enhanced_layouts.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/views/place_details_screen.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:dio/dio.dart';
import 'package:hive/hive.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isLoading = false; // Track loading state
  bool _isLoadingMore = false; // Track loading more state
  List<Map<String, dynamic>> _searchResults = []; // Store search results
  late final DioConsumer dioConsumer; // Declare DioConsumer

  // Pagination variables
  int _currentPage = 1;
  bool _hasMore = true;
  String _lastSearchQuery = '';

  @override
  void initState() {
    super.initState();
    // Initialize DioConsumer using getIt dependencies
    dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );

    // Add scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMore && _lastSearchQuery.isNotEmpty) {
        _loadMoreResults();
      }
    }
  }

  // Fetch search results from the API using DioConsumer
  Future<void> _fetchSearchResults(String query, {bool isLoadMore = false}) async {
    if (isLoadMore) {
      setState(() {
        _isLoadingMore = true;
      });
    } else {
      setState(() {
        _isLoading = true;
        _currentPage = 1;
        _hasMore = true;
        _searchResults.clear();
      });
    }

    try {
      final response = await dioConsumer.get(
        '/api/items/search',
        queryParameters: {
          'keyword': query,
          'page': _currentPage,
          'limit': 20,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        print('DEBUG: Raw data: $data');

        final List<Map<String, dynamic>> results =
            List<Map<String, dynamic>>.from(data['items'] ?? []);

        print('DEBUG: Parsed results count: ${results.length}');
        print('DEBUG: First result: ${results.isNotEmpty ? results.first : 'No results'}');

        final pagination = data['pagination'];
        print('DEBUG: Pagination: $pagination');

        setState(() {
          if (isLoadMore) {
            _searchResults.addAll(results);
          } else {
            _searchResults = results;
            _lastSearchQuery = query;
          }

          _hasMore = pagination['has_more'] ?? false;
          _currentPage = pagination['current_page'] ?? 1;

          print('DEBUG: _searchResults count after setState: ${_searchResults.length}');
        });
      } else {
        if (mounted && !isLoadMore) {
          final s = S.of(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                s.noData,
                style: AppTextStyles.font14Regular.copyWith(color: Colors.white),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        final s = S.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${s.error}: $e',
              style: AppTextStyles.font14Regular.copyWith(color: Colors.white),
            ),
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  // Load more results for pagination
  Future<void> _loadMoreResults() async {
    if (_hasMore && !_isLoadingMore && _lastSearchQuery.isNotEmpty) {
      _currentPage++;
      await _fetchSearchResults(_lastSearchQuery, isLoadMore: true);
    }
  }

  // Perform search when the user submits the query
  void _performSearch() {
    final String query = _searchController.text.trim();

    if (query.isEmpty) {
      if (mounted) {
        final s = S.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              s.enterSearchTerm,
              style: AppTextStyles.font14Regular.copyWith(color: Colors.white),
            ),
          ),
        );
      }
      return;
    }

    _fetchSearchResults(query); // Fetch results from the API
  }

  // Navigate to PlaceDetailsScreen with selected item
  void _navigateToPlaceDetails(Map<String, dynamic> item) {
    try {
      final placeDetail = PlaceDetailModel.fromJson(item);
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PlaceDetailsScreen(placeDetail: placeDetail),
        ),
      );
    } catch (e) {
      if (mounted) {
        final s = S.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${s.error}: $e',
              style: AppTextStyles.font14Regular.copyWith(color: Colors.white),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.search,
      showBackButton: false,
      hasBottomNavigation: true,
      body: Column(
        children: [
          // Enhanced Search Bar
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: context.accentColor.withValues(alpha: 0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              onSubmitted: (_) => _performSearch(),
              style: AppTextStyles.font16Regular.copyWith(
                color: context.primaryTextColor,
              ),
              decoration: InputDecoration(
                hintText: s.searchPlaceholder,
                hintStyle: AppTextStyles.font16Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
                prefixIcon: Container(
                  padding: const EdgeInsets.all(14),
                  child: Icon(
                    Icons.search_rounded,
                    color: context.accentColor,
                    size: 24,
                  ),
                ),
                suffixIcon: _isLoading
                  ? Container(
                      padding: const EdgeInsets.all(14),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            context.accentColor,
                          ),
                        ),
                      ),
                    )
                  : Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            context.accentColor,
                            context.accentColor.withValues(alpha: 0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(14),
                        boxShadow: [
                          BoxShadow(
                            color: context.accentColor.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: IconButton(
                        onPressed: _performSearch,
                        icon: const Icon(
                          Icons.search_rounded,
                          color: Colors.white,
                          size: 20,
                        ),
                        tooltip: s.search,
                      ),
                    ),
                filled: true,
                fillColor: context.cardColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide(
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    width: 1,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide(
                    color: context.accentColor,
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 18,
                  horizontal: 20,
                ),
              ),
            ),
          ),

          // Enhanced Search Results
          Expanded(
            child: Builder(
              builder: (context) {
                print('DEBUG: Building UI - _isLoading: $_isLoading, _searchResults.length: ${_searchResults.length}');

                if (_isLoading) {
                  return EnhancedLoading(message: s.loading);
                }

                if (_searchResults.isEmpty) {
                  return EnhancedEmptyState(
                    icon: Icons.search_off_rounded,
                    title: s.noResults,
                    subtitle: s.tryDifferentSearch,
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _searchResults.length + (_isLoadingMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    // Show loading indicator at the end
                    if (index == _searchResults.length) {
                      return const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final item = _searchResults[index];
                    return EnhancedCard(
                      margin: const EdgeInsets.only(bottom: 12),
                      onTap: () => _navigateToPlaceDetails(item),
                      child: Row(
                        children: [
                          // Image
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: item['image'] != null
                                ? Image.network(
                                    item['image'],
                                    width: 80,
                                    height: 80,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) =>
                                        Container(
                                      width: 80,
                                      height: 80,
                                      decoration: BoxDecoration(
                                        color: context.secondaryTextColor
                                            .withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Icon(
                                        Icons.image_not_supported_rounded,
                                        color: context.secondaryTextColor,
                                      ),
                                    ),
                                  )
                                : Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      color: context.accentColor
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.search_rounded,
                                      color: context.accentColor,
                                      size: 32,
                                    ),
                                  ),
                          ),
                          const SizedBox(width: 16),
                          // Content
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item['title'] ?? s.noTitle,
                                  style: AppTextStyles.font16Bold.copyWith(
                                    color: context.primaryTextColor,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  item['content'] ?? s.noDescription,
                                  style: AppTextStyles.font14Regular.copyWith(
                                    color: context.secondaryTextColor,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          // Arrow
                          Icon(
                            Icons.arrow_forward_ios_rounded,
                            color: context.secondaryTextColor,
                            size: 16,
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ),
          ),
        ],
      ),
    );
  }
}