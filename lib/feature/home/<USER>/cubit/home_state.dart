part of 'home_cubit.dart';

abstract class HomeState extends Equatable {
  const HomeState();

  @override
  List<Object?> get props => [];
}

class HomeInitial extends HomeState {}

class HomeLoading extends HomeState {}

class HomeLoaded extends HomeState {
  final City? currentCity;
  final List<City> cities;
  final List<ServiceCategory> categories;
  final List<Map<String, dynamic>> reels;
  final bool isLoadingCategories;
  final bool isLoadingReels;
  final bool isSearching;
  final List<Map<String, dynamic>> searchResults;
  final Map<String, dynamic>? reelsPagination;

  const HomeLoaded({
    this.currentCity,
    this.cities = const [],
    this.categories = const [],
    this.reels = const [],
    this.isLoadingCategories = false,
    this.isLoadingReels = false,
    this.isSearching = false,
    this.searchResults = const [],
    this.reelsPagination,
  });

  HomeLoaded copyWith({
    City? currentCity,
    List<City>? cities,
    List<ServiceCategory>? categories,
    List<Map<String, dynamic>>? reels,
    bool? isLoadingCategories,
    bool? isLoadingReels,
    bool? isSearching,
    List<Map<String, dynamic>>? searchResults,
    Map<String, dynamic>? reelsPagination,
  }) {
    return HomeLoaded(
      currentCity: currentCity ?? this.currentCity,
      cities: cities ?? this.cities,
      categories: categories ?? this.categories,
      reels: reels ?? this.reels,
      isLoadingCategories: isLoadingCategories ?? this.isLoadingCategories,
      isLoadingReels: isLoadingReels ?? this.isLoadingReels,
      isSearching: isSearching ?? this.isSearching,
      searchResults: searchResults ?? this.searchResults,
      reelsPagination: reelsPagination ?? this.reelsPagination,
    );
  }

  @override
  List<Object?> get props => [
        currentCity,
        cities,
        categories,
        reels,
        isLoadingCategories,
        isLoadingReels,
        isSearching,
        searchResults,
      ];
}

class HomeError extends HomeState {
  final String message;

  const HomeError(this.message);

  @override
  List<Object> get props => [message];
}
